@import '../global';

$mainImageSize: 450px;

// Container for unauthorized state (no title or other UI elements)
.unauthorizedContainer {
    display: flex;
    justify-content: center;
    align-items: flex-start; // Position at top
    width: 100%;
    padding-top: 100px; // Add top padding to position below where the title would be
    padding-left: 20px;
    padding-right: 20px;
    box-sizing: border-box;

    // Unauthorized message
    .unauthorizedMessage {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px;
        background-color: $offBlack;
        border-radius: $smallBorderRadius;
        color: $offWhite;
        text-align: center;
        font-size: 20px;
        line-height: 1.5;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.4);
        max-width: 500px;

        p {
            margin: 10px 0;
        }
    }
}

.mainContainer {
    @include mainPageStyles;
    width: max-content;

    // Top Buttons
    .topButtonsContainer {
        position: relative;
        display: flex;
        flex-direction: row;
        gap: 18px;
        align-items: center;
        justify-content: flex-start;
        margin-top: 5px;
    }

    // Process
    .processContainer {
        @include processContainerStyles;
        @include hideScrollbar;
        position: relative;
        width: 400px;
        min-height: 583px;
        max-height: calc(100vh - 400px);
        color: $offWhite;
        overflow-y: scroll;

        // Waiting State
        &.waiting {
            background-color: $veryDarkPrimaryColor;
        }

        // Ready
        .readyText {
            position: absolute;
            top: 50%; left: 50%; transform: translate(-50%, -50%);
            color: $offWhite;
            text-align: center;
            font-size: 44px;
            user-select: none;
        }

        // Progress Items
        .progressItem {
            margin: 10px 0;
            width: 100%;
            height: max-content;
            display: flex;
            align-items: center;
            column-gap: 20px;

            // Image
            .itemImage {
                width: 60px;
                height: 60px;
                object-fit: cover;
                border-radius: $smallImageBorderRadius;
            }

            // Title
            .progressTitle {
                display: flex;
                font-size: 20px;
                color: $offWhite;
            }

            // Group Heading
            .itemGroupHeading {
                margin-left: 5px;
                margin-right: -5px;
                width: max-content;
                padding: 5px 10px;
                border-radius: $smallBorderRadius;
                background-color: $primaryColor;
                font-size: 16px;
            }

            // New Items
            .newItem {
                flex-grow: 1;
                display: flex;
                align-items: center;
                column-gap: 20px;
            }

            &:hover {
                background-color: $veryDarkPrimaryColor;
            }

            &.checked:not(.hasNewItems) {
                opacity: 0.55;
            }
        }
    }

    // Form Fields
    .fieldsContainer {
        display: flex;
        flex-direction: column;
        gap: 20px;
        margin-top: 20px;

        // Field with Spinner
        .fieldWithSpinner {
            position: relative;
            display: flex;
            align-items: center;

            .fieldSpinner {
                position: absolute;
                right: 10px;
                top: 50%;
                transform: translateY(-50%);
            }
        }

        // Input Override
        .inputOverride {
            width: 530px !important;
            &>input:not(:focus) {
                cursor: pointer !important;
            }
        }

        // Playlist Tile Container
        .playlistTileContainer {
            width: 120px;
            display: flex;
            justify-content: flex-start;
            margin: 10px 10px;

            .playlistTile {
                width: 100%;
                height: max-content;
                display: flex;
                flex-direction: column;
                align-items: center;
                border-radius: $smallImageBorderRadius;
                cursor: pointer;

                &:hover {
                    background-color: $veryDarkPrimaryColor;
                }

                .playlistTileImage {
                    width: 100%;
                    height: auto;
                    object-fit: cover;
                    border-radius: $smallImageBorderRadius;
                    margin-bottom: 8px;
                }

                .playlistTileTitle {
                    width: 100%;
                    text-align: center;
                    padding: 2px 4px 6px 4px;
                    font-size: 12px;
                    color: $offWhite;
                    line-height: 1.2;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }

            .playlistTilePlaceholder {
                width: 120px;
                height: max-content;
                display: flex;
                flex-direction: column;
                align-items: center;

                .playlistTilePlaceholderImage {
                    width: 120px;
                    height: 120px;
                    background-color: $veryDarkPrimaryColor;
                    border-radius: $smallImageBorderRadius;
                    margin-bottom: 8px;
                }

                .playlistTilePlaceholderTitle {
                    width: 120px;
                    height: 20px;
                    background-color: $veryDarkPrimaryColor;
                    border-radius: $tinyBorderRadius;
                    margin-top: 2px;
                }
            }
        }

        // Not Signed In Text
        .notSignedInText {
            font-size: 14px;
            color: $offWhite;
            text-align: center;
            margin-top: 10px;
        }
    }

    // Buttons (legacy - keeping for compatibility)
    .buttonsContainerOverride {
        align-items: flex-start;

        // Artist URL
        .inputOverride {
            width: 530px !important;
            &>input:not(:focus) {
                cursor: pointer !important;
            }
        }

        // Summary Text
        .summaryText {
            position: relative;
            display: grid;
            grid-template-columns: max-content max-content;
            align-items: flex-end;
            column-gap: 20px;
            font-size: 28px;
            color: $primaryColor;

            a { color: $primaryColor; }
            a:hover { font-weight: bolder; }
        }

        .notSignedInText {
            font-size: 14px;
            position: absolute;
            bottom: -24px;
            left: 2px;
            width: max-content;
        }
    }

    // Show New
    .showNewContainer {
        margin-left: 10px;
    }
}