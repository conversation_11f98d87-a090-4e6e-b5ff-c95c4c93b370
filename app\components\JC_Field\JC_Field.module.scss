@import '../../global';

$leftGap: 10px;
$normalInputWidth: 350px;

.mainContainer {

    input[disabled] { pointer-events:none } // So parent clicks trigger properly

    // Label
    .label {
        margin-bottom: 6px;
        padding-left: 11px;
        text-align: left;
        font-size: $defaultFontSize;
        font-weight: bold;
        color: $pastelPrimaryColor;

        // Error Message
        .errorSpan {
            color: $errorColor;
            font-weight: bold;
            padding-left: 10px;
            position: absolute;
            // opacity: 0;
            // animation: errorFadeOutAnimation 2.5s;
        }
    }

    // Input
    .inputContainer {
        outline: solid $tinyBorderWidth $offBlack;
        border-radius: $tinyBorderRadius;
        margin-top: 2px;
        margin-left: 2px;
        width: $normalInputWidth;
        max-width: 100%;
        height: 40px;
        background-color: transparent;
        border: solid $smallBorderWidth $primaryColor;

        // Input
        input,
        textarea {
            width: 100%;
            height: 100%;
            padding-left: 10px;
            box-sizing: border-box;
            outline: none !important;
            border: none !important;
            background-color: transparent;
            font-size: $defaultFontSize;
            color: $offWhite;
            letter-spacing: 1px;
            resize: none;
        }
        textarea {
            padding-top: 8px;
            @include hideScrollBar;
        }

        // Type: Text
        &.textType {
            display: block;
        }

        // Type: Number
        &.numberType {
            width: 70px;
        }

        // Type: Password
        &.passwordType {
            display: block;
        }

        // Type: Textarea
        &.textareaType  {
            width: $normalInputWidth;
            height: 130px;
        }

        // Type: Rich Text
        &.richTextType  {
            width: 280px;
            height: 160px;
            font-size: 15px;
            display: flex;
            flex-direction: column;
            // Buttons
            .richTextButtons {
                width: 100%;
                display: flex;
                column-gap: 10px;
                box-sizing: border-box;
                padding: 8px;
                border-bottom: solid $smallBorderWidth $offBlack;
                button {
                    padding: 5px 10px 5px 9px;
                    width: 30px;
                    height: 29px;
                    background-color: transparent;
                    border-radius: $tinyBorderRadius;
                    cursor: pointer;
                }
                .colourButton {
                    width: 50px;
                    height: 29px;
                    background-color: transparent;
                    cursor: pointer;
                }
                button.isActive {
                    background-color: $pastelSecondaryColor;
                }
            }
            > div:last-child {
                overflow-y: auto;
                flex-grow: 1;
                background-color: $offWhite;
                > div {
                    height: 100%;
                    padding: 10px;
                    box-sizing: border-box;
                    outline: none !important;
                    > p {
                        margin: 0;
                    }
                }
            }
        }
    }

    // Read-Only
    &.readOnly {
        opacity: 0.7;
        .inputContainer {
            background-color: rgb(59, 59, 59);
        }
    }

}

// Error Fade Out Animation
@keyframes errorFadeOutAnimation {
    0%   { opacity: 1; }
    80%  { opacity: 1; }
    100% { opacity: 0; }
}