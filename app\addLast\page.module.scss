@import '../global';

$mainImageSize: 450px;

// Container for unauthorized state (no title or other UI elements)
.unauthorizedContainer {
    display: flex;
    justify-content: center;
    align-items: flex-start; // Changed from center to flex-start to position at top
    width: 100%;
    padding-top: 100px; // Add top padding to position below where the title would be
    padding-left: 20px;
    padding-right: 20px;
    box-sizing: border-box;

    // Unauthorized message
    .unauthorizedMessage {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px;
        background-color: $offBlack;
        border-radius: $smallBorderRadius;
        color: $offWhite;
        text-align: center;
        font-size: 20px;
        line-height: 1.5;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.4);
        max-width: 500px;

        p {
            margin: 10px 0;
        }
    }
}

.mainContainer {
    @include mainPageStyles;
    width: max-content;
    overflow-x: hidden;

    // Top Buttons
    .topButtonsContainer {
        position: relative;
        display: flex;
        flex-direction: row;
        gap: 18px;
        align-items: center;
        justify-content: flex-start;
        margin-top: 5px;

        // Run Button
        .runButtonOverride {
            margin-top: 0;
        }

        // Spinner
        .spinner {
            width: max-content;
            height: 100%;
            img {
                left: 450px;
                top: -52px;
            }
            &.hidden {
                visibility: hidden;
            }
        }
    }


    // Last Song + Process
    .lastSongAndProcessContainer {
        display: flex;
        flex-direction: column;
        row-gap: 20px;
        width: 400px;

        // Last Song
        .lastSong {
            @include processContainerStyles;
            width: 100%;
            height: 120px;
            display: flex;
            align-items: center;
            column-gap: 20px;
            overflow: hidden;

            &:hover {
                background-color: $veryDarkPrimaryColor;
            }

            // Image
            .lastSongImage {
                width: auto;
                height: 100%;
                object-fit: cover;
                border-radius: $smallImageBorderRadius;
            }

            // Text
            .lastSongText {
                font-size: 20px;
                color: $offWhite;

                &>div:last-child {
                    margin-top: 5px;
                    color: $primaryColor;
                }
            }
        }

        // Selected Playlists Row
        .selectedPlaylistsContainer {
            @include hideScrollbar;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            overflow-x: auto;
            padding: 10px 10px 8px 10px;
            width: 95%;
            box-sizing: border-box;
            margin: auto;

            // Selected Playlist Tile - matches main playlist tile sizing exactly
            .selectedPlaylistTile {
                flex-shrink: 0;
                position: relative;
                width: 70px;
                height: max-content;
                display: flex;
                flex-direction: column;
                align-items: center;
                user-select: none;

                .selectedPlaylistContent {
                    width: 100%;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    border-radius: $smallImageBorderRadius;
                    cursor: pointer;
                    // No background - borderless as requested

                    &:hover {
                        background-color: $veryDarkPrimaryColor;
                    }

                    // Image - same size as main playlist tiles
                    .selectedPlaylistImage {
                        width: 70px;
                        height: 70px;
                        object-fit: cover;
                        border-radius: $smallImageBorderRadius;
                        margin-bottom: 5px;
                    }

                    // Title Container - matches main playlist styling
                    .selectedPlaylistTitle {
                        position: relative;
                        width: 70px;
                        text-align: center;
                        padding: 2px 4px 6px 4px;
                        font-size: 12px;
                        color: $offWhite;
                        line-height: 1.2;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        width: 100%;
                    }
                }
            }
        }

        // Process
        .progressContainer {
            @include processContainerStyles;
            @include hideScrollbar;
            position: relative;
            width: 100%;
            min-height: 440px;
            max-height: calc(100vh - 257px);
            color: $offWhite;
            overflow-y: scroll;

            // Waiting State
            &.waiting {
                background-color: $veryDarkPrimaryColor;
            }

            // Ready
            .readyText {
                position: absolute;
                top: 50%; left: 50%; transform: translate(-50%, -50%);
                color: $offWhite;
                text-align: center;
                font-size: 44px;
                user-select: none;
            }

            // Group Heading
            .itemGroupHeading {
                float: left;
                margin: 18px 0;
                grid-column: 1 / 3;
                width: max-content;
                padding: 5px 10px;
                border-radius: $smallBorderRadius;
                background-color: $primaryColor;
                font-size: 16px;
            }

            // Progress Items
            .progressItem {
                margin: 10px 0;
                width: 100%;
                height: max-content;
                display: flex;
                align-items: center;
                column-gap: 20px;

                &:hover {
                    background-color: $veryDarkPrimaryColor;
                }

                // Image
                .itemImage {
                    width: 60px;
                    height: 60px;
                    object-fit: cover;
                    border-radius: $smallImageBorderRadius;
                }

                // Title
                .progressTitle {
                    display: flex;
                    font-size: 20px;
                    color: $offWhite;
                }
            }
        }

        // Auto-scrolling All Playlists (when none selected)
        .autoScrollPlaylistsContainer {
            width: 95%;
            overflow: hidden;
            padding: 10px 0 8px 0;
            box-sizing: border-box;
            margin: auto;
            position: relative;

            .autoScrollPlaylistsTrack {
                display: flex;
                align-items: center;
                gap: 15px;
                animation: autoScrollLeft 40s linear infinite;
                width: max-content;
                will-change: transform;

                // Auto-scroll Playlist Tile - matches main playlist tile sizing exactly
                .autoScrollPlaylistTile {
                    flex-shrink: 0;
                    position: relative;
                    width: 70px;
                    height: max-content;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    user-select: none;
                    opacity: 0.45;
                    cursor: pointer;

                    .autoScrollPlaylistContent {
                        width: 100%;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        border-radius: $smallImageBorderRadius;

                        &:hover {
                            background-color: $veryDarkPrimaryColor;
                        }

                        // Image - same size as main playlist tiles
                        .autoScrollPlaylistImage {
                            width: 70px;
                            height: 70px;
                            object-fit: cover;
                            border-radius: $smallImageBorderRadius;
                            margin-bottom: 5px;
                        }

                        // Title Container - matches main playlist styling
                        .autoScrollPlaylistTitle {
                            position: relative;
                            width: 70px;
                            text-align: center;
                            padding: 2px 4px 6px 4px;
                            font-size: 12px;
                            color: $offWhite;
                            line-height: 1.2;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            width: 100%;
                        }
                    }
                }
            }
        }
    }

    // Auto-scroll animation
    @keyframes autoScrollLeft {
        0% {
            transform: translateX(0);
        }
        100% {
            transform: translateX(-50%);
        }
    }

    // Playlists + Run
    .playlistsAndRunContainer {
        position: relative;
        width: max-content;

        // Playlists
        .playlistsContainer {
            @include processContainerStyles;
            padding: 0;
            width: max-content;
            height: 100%;
            min-height: 400px;
            max-height: calc(100vh - 400px);
            display: flex;
            flex-direction: column;
            overflow: hidden;

            // Search Filter Display (inside container)
            .searchFilterDisplay {
                background-color: $primaryColor;
                color: $offBlack;
                padding: 12px 14px;
                font-size: 16px;
                font-weight: bold;
                text-align: center;
                user-select: none;
                width: 100%;
                box-sizing: border-box;
                flex-shrink: 0;
            }

            // Grid container for playlist items
            .playlistGrid {
                @include hideScrollbar;
                display: grid;
                grid-template-columns: repeat(3, 70px);
                gap: 15px;
                justify-content: center;
                flex-grow: 1;
                overflow-y: scroll;
                padding: 14px;
            }

            // Playlist Tile
            .playlistItem {
                position: relative;
                width: 70px;
                height: max-content;
                display: flex;
                flex-direction: column;
                align-items: center;
                user-select: none;

                // Image + Title Container
                .imageAndTitleContainer, .imageAndTitleContainerEditMode {
                    width: 100%;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    border-radius: $smallImageBorderRadius;
                    cursor: pointer;

                    &.hidden {
                        opacity: 0.45;
                    }

                    // Image
                    .itemImage {
                        width: 70px;
                        height: 70px;
                        object-fit: cover;
                        border-radius: $smallImageBorderRadius;
                        margin-bottom: 5px;
                    }

                    // Title Container with Chevron
                    .playlistTitleContainer {
                        position: relative;
                        width: 70px;
                        text-align: center;
                        padding: 2px 4px 6px 4px;

                        // Title
                        .playlistTitle {
                            font-size: 12px;
                            color: $offWhite;
                            line-height: 1.2;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            width: 100%;
                        }


                    }
                }

                .imageAndTitleContainer {
                    &.selected {
                        background-color: $darkPrimaryColor;
                        outline: $smallBorderWidth solid $darkPrimaryColor;
                    }

                    &:not(.selected):hover {
                        background-color: $veryDarkPrimaryColor;
                    }
                }

                .imageAndTitleContainerEditMode {
                    &:hover {
                        background-color: $veryDarkPrimaryColor;
                    }
                }
            }
        }

        // Edit, Save, Cancel buttons (centered)
        .allNoneButtonOverride {
            margin: 20px auto;
            background-color: $primaryColor;
        }

        // Cancel and Save buttons container (row layout)
        .cancelSaveButtonsContainer {
            display: flex;
            flex-direction: row;
            gap: 16px;
            justify-content: center;
            margin: 20px auto;

            // Override button margins within the container
            .allNoneButtonOverride {
                margin: 0;
            }
        }

    }

    // Context Menu
    .contextMenuOverlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 999;
    }

    .contextMenu {
        position: fixed;
        background-color: $darkPrimaryColor;
        border: 1px solid $primaryColor;
        border-radius: $smallImageBorderRadius;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        z-index: 1000;
        min-width: 150px;
        padding: 5px 6px;

        .contextMenuItem {
            padding: 8px 16px;
            color: $offWhite;
            font-size: 14px;
            cursor: pointer;
            user-select: none;

            &:hover {
                background-color: rgba($primaryColor, 0.2);
            }
        }
    }

    .modalContainer {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        row-gap: 20px;
    }

}